/**
 * Event Ingestion Utility for Credit Chakra Platform
 * 
 * Provides manual event ingestion capabilities for trigger engine testing:
 * - Single event ingestion
 * - Batch event ingestion
 * - Event validation and processing
 * - Support for T-01, T-04, T-03 trigger events
 */

const { v4: uuidv4 } = require('uuid');
const firestoreManager = require('../firestore/init');
const { Validators } = require('../utils/validators');
const logger = require('../utils/logger');
require('dotenv').config();

class EventIngestor {
    constructor() {
        this.collectionName = process.env.COLLECTION_RAW_EVENTS || 'raw_events';
        this.msmeCollectionName = process.env.COLLECTION_MSMES || 'msmes';
    }

    /**
     * Get existing MSME IDs for validation
     */
    async getValidMSMEIds() {
        try {
            const db = firestoreManager.getDb();
            const msmeSnapshot = await db.collection(this.msmeCollectionName).get();
            
            const msmeIds = [];
            msmeSnapshot.forEach(doc => {
                msmeIds.push(doc.data().msme_id);
            });

            return msmeIds;
        } catch (error) {
            logger.error('Failed to fetch MSME IDs:', error);
            throw error;
        }
    }

    /**
     * Create EMI bounce event template
     */
    createEMIBounceTemplate(msmeId) {
        return {
            event_id: uuidv4(),
            msme_id: msmeId,
            event_type: 'EMI_BOUNCE',
            event_data: {
                loan_account_number: `LA${Math.floor(Math.random() * **********)}`,
                emi_amount: 25000,
                due_date: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000), // 5 days ago
                bounce_reason: 'Insufficient Funds',
                bank_name: 'HDFC Bank',
                bounce_charges: 750,
                consecutive_bounces: 1
            },
            source: 'bank',
            status: 'unprocessed',
            severity: 'high',
            timestamp: new Date(),
            created_at: new Date()
        };
    }

    /**
     * Create GST delay event template
     */
    createGSTDelayTemplate(msmeId) {
        return {
            event_id: uuidv4(),
            msme_id: msmeId,
            event_type: 'GST_DELAY',
            event_data: {
                return_period: '12/2024',
                due_date: new Date(Date.now() - 20 * 24 * 60 * 60 * 1000), // 20 days ago
                filed_date: null,
                delay_days: 20,
                penalty_amount: 2000,
                return_type: 'GSTR-3B',
                tax_liability: 45000,
                status: 'not_filed'
            },
            source: 'gst_portal',
            status: 'unprocessed',
            severity: 'medium',
            timestamp: new Date(),
            created_at: new Date()
        };
    }

    /**
     * Create cashflow dip event template
     */
    createCashflowDipTemplate(msmeId) {
        return {
            event_id: uuidv4(),
            msme_id: msmeId,
            event_type: 'CASHFLOW_DIP',
            event_data: {
                analysis_period: '30_days',
                previous_avg_balance: 350000,
                current_avg_balance: 180000,
                dip_percentage: 48,
                consecutive_low_days: 12,
                minimum_balance: 25000,
                account_number: `AC${Math.floor(Math.random() * **********)}`,
                bank_name: 'ICICI Bank',
                trend: 'declining'
            },
            source: 'bank',
            status: 'unprocessed',
            severity: 'high',
            timestamp: new Date(),
            created_at: new Date()
        };
    }

    /**
     * Ingest a single event
     */
    async ingestSingleEvent(eventData) {
        try {
            logger.logOperation('Ingesting single event', { event_type: eventData.event_type });

            // Initialize Firestore
            await firestoreManager.initialize();
            const db = firestoreManager.getDb();
            const collection = db.collection(this.collectionName);

            // Validate event data
            const validation = Validators.validateRawEvent(eventData);
            if (!validation.isValid) {
                throw new Error(`Event validation failed: ${JSON.stringify(validation.errors)}`);
            }

            // Convert dates to Firestore timestamps
            const admin = firestoreManager.getAdmin();
            const firestoreData = {
                ...validation.data,
                timestamp: admin.firestore.Timestamp.fromDate(validation.data.timestamp),
                created_at: admin.firestore.Timestamp.fromDate(validation.data.created_at)
            };

            if (validation.data.processed_at) {
                firestoreData.processed_at = admin.firestore.Timestamp.fromDate(validation.data.processed_at);
            }

            // Save to Firestore
            await collection.doc(eventData.event_id).set(firestoreData);

            logger.logSuccess('Event ingested successfully', {
                event_id: eventData.event_id,
                event_type: eventData.event_type,
                msme_id: eventData.msme_id
            });

            return {
                success: true,
                event_id: eventData.event_id,
                event: validation.data
            };

        } catch (error) {
            logger.logError('Event ingestion failed', error);
            throw error;
        }
    }

    /**
     * Ingest multiple events in batch
     */
    async ingestBatchEvents(events) {
        try {
            logger.logOperation('Ingesting batch events', { count: events.length });

            // Initialize Firestore
            await firestoreManager.initialize();
            const db = firestoreManager.getDb();
            const collection = db.collection(this.collectionName);

            // Validate all events
            const validationResults = events.map(event => Validators.validateRawEvent(event));
            const validEvents = validationResults.filter(result => result.isValid).map(result => result.data);
            const invalidCount = validationResults.length - validEvents.length;

            if (invalidCount > 0) {
                logger.warn(`${invalidCount} invalid events found and skipped`);
            }

            // Batch write to Firestore
            const batchSize = 500; // Firestore batch limit
            const admin = firestoreManager.getAdmin();
            const results = [];

            for (let i = 0; i < validEvents.length; i += batchSize) {
                const batch = db.batch();
                const batchEvents = validEvents.slice(i, i + batchSize);

                for (const event of batchEvents) {
                    const docRef = collection.doc(event.event_id);
                    
                    // Convert dates to Firestore timestamps
                    const firestoreData = {
                        ...event,
                        timestamp: admin.firestore.Timestamp.fromDate(event.timestamp),
                        created_at: admin.firestore.Timestamp.fromDate(event.created_at)
                    };

                    if (event.processed_at) {
                        firestoreData.processed_at = admin.firestore.Timestamp.fromDate(event.processed_at);
                    }

                    batch.set(docRef, firestoreData);
                    results.push(event.event_id);
                }

                await batch.commit();
                logger.info(`Committed batch ${Math.floor(i / batchSize) + 1} with ${batchEvents.length} events`);
            }

            logger.logSuccess('Batch event ingestion completed', {
                total: events.length,
                ingested: validEvents.length,
                skipped: invalidCount
            });

            return {
                success: true,
                ingested: validEvents.length,
                skipped: invalidCount,
                event_ids: results
            };

        } catch (error) {
            logger.logError('Batch event ingestion failed', error);
            throw error;
        }
    }

    /**
     * Generate and ingest test events for trigger engine
     */
    async ingestTestEvents(msmeId, eventTypes = ['EMI_BOUNCE', 'GST_DELAY', 'CASHFLOW_DIP']) {
        try {
            logger.logOperation('Generating test events for trigger engine', { msme_id: msmeId, event_types: eventTypes });

            // Validate MSME ID
            const validMSMEIds = await this.getValidMSMEIds();
            if (!validMSMEIds.includes(msmeId)) {
                throw new Error(`Invalid MSME ID: ${msmeId}`);
            }

            const events = [];

            for (const eventType of eventTypes) {
                let eventData;
                
                switch (eventType) {
                    case 'EMI_BOUNCE':
                        eventData = this.createEMIBounceTemplate(msmeId);
                        break;
                    case 'GST_DELAY':
                        eventData = this.createGSTDelayTemplate(msmeId);
                        break;
                    case 'CASHFLOW_DIP':
                        eventData = this.createCashflowDipTemplate(msmeId);
                        break;
                    default:
                        logger.warn(`Unknown event type: ${eventType}`);
                        continue;
                }

                events.push(eventData);
            }

            const result = await this.ingestBatchEvents(events);

            logger.logSuccess('Test events generated and ingested', {
                msme_id: msmeId,
                events_created: result.ingested
            });

            return result;

        } catch (error) {
            logger.logError('Test event generation failed', error);
            throw error;
        }
    }

    /**
     * Interactive CLI for event ingestion
     */
    async runInteractiveCLI() {
        try {
            console.log('\n🔥 Credit Chakra Event Ingestion Utility');
            console.log('=========================================\n');

            // Get available MSMEs
            const msmeIds = await this.getValidMSMEIds();
            if (msmeIds.length === 0) {
                console.log('❌ No MSMEs found. Please seed MSMEs first.');
                return;
            }

            console.log(`📊 Found ${msmeIds.length} MSMEs available for event ingestion`);
            console.log(`🎯 First MSME ID: ${msmeIds[0]}`);

            // Generate test events for the first MSME
            const testMsmeId = msmeIds[0];
            console.log(`\n🧪 Generating test events for MSME: ${testMsmeId}`);

            const result = await this.ingestTestEvents(testMsmeId);

            console.log('\n✅ Event ingestion completed successfully!');
            console.log(`📈 Events ingested: ${result.ingested}`);
            console.log(`⚠️  Events skipped: ${result.skipped}`);
            console.log(`🆔 Event IDs: ${result.event_ids.slice(0, 3).join(', ')}${result.event_ids.length > 3 ? '...' : ''}`);

        } catch (error) {
            console.error('\n❌ Event ingestion failed:', error.message);
            throw error;
        }
    }
}

// Run CLI if this file is executed directly
if (require.main === module) {
    (async () => {
        try {
            const ingestor = new EventIngestor();
            
            // Parse command line arguments
            const args = process.argv.slice(2);
            
            if (args.length === 0) {
                // Interactive mode
                await ingestor.runInteractiveCLI();
            } else {
                // Command line mode
                const msmeId = args[0];
                const eventTypes = args.slice(1);
                
                if (eventTypes.length === 0) {
                    eventTypes.push('EMI_BOUNCE', 'GST_DELAY', 'CASHFLOW_DIP');
                }
                
                const result = await ingestor.ingestTestEvents(msmeId, eventTypes);
                console.log('Event ingestion result:', result);
            }
            
            process.exit(0);
        } catch (error) {
            console.error('Event ingestion failed:', error);
            process.exit(1);
        }
    })();
}

module.exports = EventIngestor;
