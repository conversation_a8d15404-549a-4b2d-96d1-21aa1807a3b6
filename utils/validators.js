/**
 * Joi Validation Schemas for Credit Chakra Platform
 * 
 * Defines validation schemas matching FastAPI backend models for:
 * - MSME records
 * - Raw events for trigger engine
 * - Alerts with severity levels
 * - Chakra score history
 * - Consent ledger entries
 */

const Joi = require('joi');
const logger = require('./logger');

// Common validation patterns
const patterns = {
    // Indian GSTIN format: 15-digit alphanumeric
    gstin: /^[0-9]{2}[A-Z]{5}[0-9]{4}[A-Z]{1}[1-9A-Z]{1}Z[0-9A-Z]{1}$/,
    
    // Indian mobile number with +91
    mobileNumber: /^\+91[6-9]\d{9}$/,
    
    // UUID v4 format
    uuid: /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i,
    
    // Email format
    email: /^[^\s@]+@[^\s@]+\.[^\s@]+$/
};

// MSME validation schema
const msmeSchema = Joi.object({
    msme_id: Joi.string().pattern(patterns.uuid).required(),
    name: Joi.string().min(2).max(100).required(),
    gstin: Joi.string().pattern(patterns.gstin).required(),
    chakra_score: Joi.number().integer().min(300).max(900).required(),
    chakra_band: Joi.string().valid('A', 'B', 'C', 'D', 'E').required(),
    rm_id: Joi.string().pattern(patterns.uuid).required(),
    partner_id: Joi.string().pattern(patterns.uuid).required(),
    status: Joi.string().valid('active', 'inactive', 'suspended', 'pending').default('active'),
    onboarded_at: Joi.date().required(),
    contact_person: Joi.string().min(2).max(50).optional(),
    mobile_number: Joi.string().pattern(patterns.mobileNumber).optional(),
    email: Joi.string().pattern(patterns.email).optional(),
    address: Joi.object({
        street: Joi.string().max(200).optional(),
        city: Joi.string().max(50).optional(),
        state: Joi.string().max(50).optional(),
        pincode: Joi.string().length(6).pattern(/^\d{6}$/).optional()
    }).optional(),
    business_type: Joi.string().valid('manufacturing', 'trading', 'service', 'retail').optional(),
    annual_turnover: Joi.number().positive().optional(),
    created_at: Joi.date().default(() => new Date()),
    updated_at: Joi.date().default(() => new Date())
});

// Raw events validation schema for trigger engine
const rawEventSchema = Joi.object({
    event_id: Joi.string().pattern(patterns.uuid).required(),
    msme_id: Joi.string().pattern(patterns.uuid).required(),
    event_type: Joi.string().valid(
        'EMI_BOUNCE',           // T-01
        'GST_DELAY',            // T-04
        'CASHFLOW_DIP',         // T-03
        'PAYMENT_DEFAULT',
        'CREDIT_INQUIRY',
        'BANK_STATEMENT_ANALYSIS',
        'GST_FILING',
        'LOAN_APPLICATION'
    ).required(),
    event_data: Joi.object().required(),
    source: Joi.string().valid('bank', 'gst_portal', 'credit_bureau', 'internal', 'partner').required(),
    status: Joi.string().valid('unprocessed', 'processing', 'processed', 'failed').default('unprocessed'),
    severity: Joi.string().valid('high', 'medium', 'low').required(),
    timestamp: Joi.date().required(),
    processed_at: Joi.date().optional(),
    created_at: Joi.date().default(() => new Date())
});

// Alerts validation schema
const alertSchema = Joi.object({
    alert_id: Joi.string().pattern(patterns.uuid).required(),
    msme_id: Joi.string().pattern(patterns.uuid).required(),
    event_id: Joi.string().pattern(patterns.uuid).required(),
    trigger_rule: Joi.string().valid('T-01', 'T-03', 'T-04').required(),
    alert_type: Joi.string().required(),
    severity: Joi.string().valid('high', 'medium', 'low').required(),
    score_impact: Joi.number().integer().min(-50).max(0).required(),
    message: Joi.string().min(10).max(500).required(),
    status: Joi.string().valid('active', 'acknowledged', 'resolved', 'dismissed').default('active'),
    created_at: Joi.date().default(() => new Date()),
    acknowledged_at: Joi.date().optional(),
    acknowledged_by: Joi.string().pattern(patterns.uuid).optional(),
    resolved_at: Joi.date().optional(),
    metadata: Joi.object().optional()
});

// Chakra score validation schema
const chakraScoreSchema = Joi.object({
    score_id: Joi.string().pattern(patterns.uuid).required(),
    msme_id: Joi.string().pattern(patterns.uuid).required(),
    score: Joi.number().integer().min(300).max(900).required(),
    band: Joi.string().valid('A', 'B', 'C', 'D', 'E').required(),
    previous_score: Joi.number().integer().min(300).max(900).optional(),
    score_change: Joi.number().integer().min(-100).max(100).optional(),
    factors: Joi.array().items(Joi.object({
        factor: Joi.string().required(),
        impact: Joi.number().required(),
        weight: Joi.number().min(0).max(1).required()
    })).optional(),
    calculated_at: Joi.date().required(),
    valid_until: Joi.date().required(),
    created_at: Joi.date().default(() => new Date())
});

// Consent ledger validation schema
const consentLedgerSchema = Joi.object({
    consent_id: Joi.string().pattern(patterns.uuid).required(),
    msme_id: Joi.string().pattern(patterns.uuid).required(),
    consent_type: Joi.string().valid(
        'data_processing',
        'credit_check',
        'bank_statement_access',
        'gst_data_access',
        'score_calculation',
        'alert_notifications'
    ).required(),
    status: Joi.string().valid('granted', 'revoked', 'expired', 'pending').required(),
    granted_at: Joi.date().optional(),
    revoked_at: Joi.date().optional(),
    expires_at: Joi.date().optional(),
    purpose: Joi.string().min(10).max(200).required(),
    data_categories: Joi.array().items(Joi.string()).required(),
    granted_by: Joi.string().pattern(patterns.uuid).optional(),
    ip_address: Joi.string().ip().optional(),
    user_agent: Joi.string().optional(),
    created_at: Joi.date().default(() => new Date()),
    updated_at: Joi.date().default(() => new Date())
});

/**
 * Validation helper functions
 */
class Validators {
    static validateMsme(data) {
        const { error, value } = msmeSchema.validate(data, { abortEarly: false });
        logger.logValidation('MSME', !error, error?.details);
        return { isValid: !error, data: value, errors: error?.details };
    }

    static validateRawEvent(data) {
        const { error, value } = rawEventSchema.validate(data, { abortEarly: false });
        logger.logValidation('RawEvent', !error, error?.details);
        return { isValid: !error, data: value, errors: error?.details };
    }

    static validateAlert(data) {
        const { error, value } = alertSchema.validate(data, { abortEarly: false });
        logger.logValidation('Alert', !error, error?.details);
        return { isValid: !error, data: value, errors: error?.details };
    }

    static validateChakraScore(data) {
        const { error, value } = chakraScoreSchema.validate(data, { abortEarly: false });
        logger.logValidation('ChakraScore', !error, error?.details);
        return { isValid: !error, data: value, errors: error?.details };
    }

    static validateConsentLedger(data) {
        const { error, value } = consentLedgerSchema.validate(data, { abortEarly: false });
        logger.logValidation('ConsentLedger', !error, error?.details);
        return { isValid: !error, data: value, errors: error?.details };
    }

    static validateBatch(schema, dataArray) {
        const results = dataArray.map(data => {
            const { error, value } = schema.validate(data, { abortEarly: false });
            return { isValid: !error, data: value, errors: error?.details };
        });

        const validCount = results.filter(r => r.isValid).length;
        const invalidCount = results.length - validCount;

        logger.info(`Batch validation completed`, {
            total: results.length,
            valid: validCount,
            invalid: invalidCount
        });

        return results;
    }
}

module.exports = {
    Validators,
    schemas: {
        msmeSchema,
        rawEventSchema,
        alertSchema,
        chakraScoreSchema,
        consentLedgerSchema
    },
    patterns
};
